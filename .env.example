# Discord Configuration
DISCORD_TOKEN=your_discord_bot_token_here
BOT_CLIENT_ID=your_bot_client_id
BOT_CLIENT_SECRET=your_bot_client_secret

# Discord OAuth Configuration (for user authentication)
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/discord_bot

# Application Configuration
PORT=8080
NODE_ENV=development
WEB_URL=http://localhost:3000

# Security Keys (Generate with: openssl rand -hex 32)
# IMPORTANT: Generate unique keys for production
USER_ENCRYPTION_KEY=generate_64_character_hex_key_here
SESSION_ENCRYPTION_KEY=generate_64_character_hex_key_here
CSRF_ENCRYPTION_KEY=generate_64_character_hex_key_here

# JWT Configuration
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h
JWT_ISSUER=discord-bot-energex

# AI Configuration (Optional)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GOOGLE_GENERATIVE_AI_API_KEY=your_google_ai_api_key_here

# Whop Integration (Optional)
WHOP_API_KEY=your_whop_api_key_here
WHOP_APP_ID=your_whop_app_id_here
WHOP_AGENT_USER_ID=your_whop_agent_user_id_here
WHOP_COMPANY_ID=your_whop_company_id_here

# Bot Configuration
DEFAULT_AI_CHANNEL=general
USE_MASTRA=true
GUILD_ID=your_test_guild_id_here
SESSION_ISOLATION_ENABLED=true

# Redis & Caching (Optional)
REDIS_URL=redis://localhost:6379
CACHE_TTL=300
CACHE_MAX_ITEMS=1000

# Security & Rate Limiting
LOG_LEVEL=info
RATE_LIMIT_TTL=60
RATE_LIMIT_MAX=100

# CORS Configuration
CORS_ORIGIN=http://localhost:3000
CORS_CREDENTIALS=true