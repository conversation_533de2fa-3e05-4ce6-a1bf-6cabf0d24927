import { Injectable, Logger, Inject } from '@nestjs/common';
import { DatabaseService } from '@/core/database';
import { ConfigService } from '@nestjs/config';



import { 
  user<PERSON><PERSON><PERSON><PERSON><PERSON>, 
  User<PERSON><PERSON><PERSON><PERSON>, 
  NewUser<PERSON><PERSON><PERSON>ey, 
  AIProvider,
  APIKeyConfig,
  APIKeyValidation
} from '@/core/database';
import * as crypto from 'crypto';
import { eq, and, or, desc, asc, count, sql } from 'drizzle-orm';

interface CreateApiKeyOptions {
  userId: string;
  guildId: string;
  provider: AIProvider;
  keyName: string;
  apiKey: string;
  displayName?: string;
  isDefault?: boolean;
  features?: string[];
  maxTokens?: number;
  dailyLimit?: number;
}

interface ValidateKeyResponse {
  isValid: boolean;
  errorMessage?: string;
  supportedModels?: string[];
  quotaInfo?: {
    used: number;
    limit: number;
    resetDate: string;
  };
}

@Injectable()
export class ApiKeyManagerService {
  private readonly logger = new Logger(ApiKeyManagerService.name);
  private readonly encryptionKey: string;
  private readonly algorithm = 'aes-256-gcm';

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly configService: ConfigService,
  ) {
    // Get encryption key from environment or generate one
    this.encryptionKey = this.configService.get<string>('API_KEY_ENCRYPTION_KEY') || 
      crypto.randomBytes(32).toString('hex');
    
    if (!this.configService.get<string>('API_KEY_ENCRYPTION_KEY')) {
      this.logger.warn('⚠️ No API_KEY_ENCRYPTION_KEY found in environment, using generated key. Set this in production!');
      this.logger.warn(`Generated key: ${this.encryptionKey}`);
    }
  }

  /**
   * Encrypt an API key for secure storage
   */
  private encrypt(text: string): string {
    const iv = crypto.randomBytes(16);
    const key = crypto.createHash('sha256').update(this.encryptionKey).digest();
    const cipher = crypto.createCipheriv(this.algorithm, key, iv);
    
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const authTag = cipher.getAuthTag ? cipher.getAuthTag().toString('hex') : '';
    
    return `${iv.toString('hex')}:${authTag}:${encrypted}`;
  }

  /**
   * Decrypt an API key for use
   */
  private decrypt(encryptedText: string): string {
    try {
      const parts = encryptedText.split(':');
      
      if (parts.length === 3) {
        const [ivHex, authTagHex, encrypted] = parts;
        const iv = Buffer.from(ivHex, 'hex');
        const authTag = Buffer.from(authTagHex, 'hex');
        
        const key = crypto.createHash('sha256').update(this.encryptionKey).digest();
    const decipher = crypto.createDecipheriv(this.algorithm, key, iv);
        if (decipher.setAuthTag) {
          decipher.setAuthTag(authTag);
        }
        
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        
        return decrypted;
      } else {
        // Fallback for simple encryption
        const key = crypto.createHash('sha256').update(this.encryptionKey).digest();
        const iv = Buffer.alloc(16, 0);  // Simple fallback IV
        const decipher = crypto.createDecipheriv('aes-256-cbc', key, iv);
        let decrypted = decipher.update(encryptedText, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return decrypted;
      }
    } catch (error) {
      this.logger.error('Failed to decrypt API key:', error);
      throw new Error('Failed to decrypt API key');
    }
  }

  /**
   * Add a new API key for a user
   */
  async addApiKey(options: CreateApiKeyOptions): Promise<UserApiKey | null> {
    try {
      const {
        userId,
        guildId,
        provider,
        keyName,
        apiKey,
        displayName,
        isDefault = false,
        features = [],
        maxTokens,
        dailyLimit
      } = options;

      // Check if key already exists
      const existingKey = await this.getUserApiKey(userId, guildId, provider, keyName);
      if (existingKey) {
        throw new Error(`API key '${keyName}' already exists for this provider`);
      }

      // If this is set as default, remove default from other keys
      if (isDefault) {
        await this.clearDefaultKeys(userId, guildId, provider);
      }

      // Encrypt the API key
      const encryptedKey = this.encrypt(apiKey);

      // Create configuration
      const config: APIKeyConfig = {
        provider,
        keyName,
        displayName: displayName || keyName,
        isActive: true,
        usageCount: 0,
        features,
        maxTokens,
        dailyLimit,
      };

      // Validate the key
      const validation = await this.validateApiKey(provider, apiKey);

      // Generate unique ID
      const keyId = `apikey_${userId}_${provider}_${Date.now()}_${Math.random().toString(36).substring(2, 8)}`;

      // Create new API key record
      const newApiKey: NewUserApiKey = {
        id: keyId,
        userId,
        guildId,
        provider,
        keyName,
        encryptedKey,
        config,
        validation,
        isActive: true,
        isDefault,
      } as NewUserApiKey;

      await this.db.insert(userApiKeys).values(newApiKey);

      // Fetch and return the created key
      const createdKey = await this.getUserApiKey(userId, guildId, provider, keyName);
      
      this.logger.log(`✅ Added API key '${keyName}' for user ${userId} (${provider})`);
      return createdKey;

    } catch (error) {
      this.logger.error('Failed to add API key:', error);
      return null;
    }
  }

  /**
   * Get a specific API key for a user
   */
  async getUserApiKey(
    userId: string, 
    guildId: string, 
    provider: AIProvider, 
    keyName: string
  ): Promise<UserApiKey | null> {
    try {
      const result = await this.db.select().from(userApiKeys)
        .where(and(
          eq(userApiKeys.userId, userId),
          eq(userApiKeys.guildId, guildId),
          eq(userApiKeys.provider, provider),
          eq(userApiKeys.keyName, keyName),
          eq(userApiKeys.isActive, true)
        ))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      this.logger.error('Failed to get user API key:', error);
      return null;
    }
  }

  /**
   * Get all API keys for a user
   */
  async getUserApiKeys(userId: string, guildId: string): Promise<UserApiKey[]> {
    try {
      return await this.db.select().from(userApiKeys)
        .where(and(
          eq(userApiKeys.userId, userId),
          eq(userApiKeys.guildId, guildId),
          eq(userApiKeys.isActive, true)
        ));
    } catch (error) {
      this.logger.error('Failed to get user API keys:', error);
      return [];
    }
  }

  /**
   * Get the default API key for a provider
   */
  async getDefaultApiKey(userId: string, guildId: string, provider: AIProvider): Promise<UserApiKey | null> {
    try {
      const result = await this.db.select().from(userApiKeys)
        .where(and(
          eq(userApiKeys.userId, userId),
          eq(userApiKeys.guildId, guildId),
          eq(userApiKeys.provider, provider),
          eq(userApiKeys.isDefault, true),
          eq(userApiKeys.isActive, true)
        ))
        .limit(1);

      return result[0] || null;
    } catch (error) {
      this.logger.error('Failed to get default API key:', error);
      return null;
    }
  }

  /**
   * Get decrypted API key for use
   */
  async getDecryptedApiKey(keyId: string): Promise<string | null> {
    try {
      const result = await this.db.select().from(userApiKeys)
        .where(and(
          eq(userApiKeys.id, keyId),
          eq(userApiKeys.isActive, true)
        ))
        .limit(1);

      if (!result[0]) return null;

      return this.decrypt(result[0].encryptedKey);
    } catch (error) {
      this.logger.error('Failed to get decrypted API key:', error);
      return null;
    }
  }

  /**
   * Update API key usage
   */
  async updateKeyUsage(keyId: string): Promise<void> {
    try {
      const key = await this.db.select().from(userApiKeys)
        .where(eq(userApiKeys.id, keyId))
        .limit(1);

      if (key[0]) {
        const updatedConfig = {
          ...key[0].config,
          usageCount: (key[0].config.usageCount || 0) + 1,
        };

        await this.db.update(userApiKeys)
          .set({
            config: updatedConfig,
            lastUsedAt: new Date(),
            updatedAt: new Date(),
          } as any)
          .where(eq(userApiKeys.id, keyId));
      }
    } catch (error) {
      this.logger.error('Failed to update key usage:', error);
    }
  }

  /**
   * Delete an API key
   */
  async deleteApiKey(userId: string, guildId: string, keyId: string): Promise<boolean> {
    try {
      const result = await this.db.update(userApiKeys)
        .set({ 
          isActive: false, 
          updatedAt: new Date() 
        } as any)
        .where(and(
          eq(userApiKeys.id, keyId),
          eq(userApiKeys.userId, userId),
          eq(userApiKeys.guildId, guildId)
        ));

      this.logger.log(`🗑️ Deleted API key ${keyId} for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to delete API key:', error);
      return false;
    }
  }

  /**
   * Validate an API key with the provider
   */
  private async validateApiKey(provider: AIProvider, apiKey: string): Promise<APIKeyValidation> {
    // Basic validation structure - would need actual API calls to providers
    const validation: APIKeyValidation = {
      isValid: true,
      lastChecked: new Date(),
    };

    try {
      switch (provider) {
        case 'openai':
          // Would make actual API call to OpenAI
          validation.supportedModels = ['gpt-4', 'gpt-3.5-turbo'];
          break;
        case 'anthropic':
          // Would make actual API call to Anthropic
          validation.supportedModels = ['claude-3-sonnet', 'claude-3-haiku'];
          break;
        case 'google':
          // Would make actual API call to Google
          validation.supportedModels = ['gemini-pro', 'gemini-pro-vision'];
          break;
        case 'exa':
          // Would make actual API call to Exa
          validation.supportedModels = ['exa-web-search', 'exa-neural-search', 'exa-content-extraction', 'exa-find-similar'];
          break;
        case 'azure':
          // Would make actual API call to Azure
          validation.supportedModels = ['azure-gpt-4o', 'azure-gpt-4-turbo', 'azure-gpt-35-turbo'];
          break;
        default:
          validation.supportedModels = ['custom-model'];
      }

      // For now, assume all keys are valid
      validation.isValid = apiKey.length > 10;
      
      if (!validation.isValid) {
        validation.errorMessage = 'API key appears to be invalid';
      }

    } catch (error) {
      validation.isValid = false;
      validation.errorMessage = (error as Error).message;
    }

    return validation;
  }

  /**
   * Clear default status from other keys
   */
  private async clearDefaultKeys(userId: string, guildId: string, provider: AIProvider): Promise<void> {
    try {
      await this.db.update(userApiKeys)
        .set({ 
          isDefault: false, 
          updatedAt: new Date() 
        } as any)
        .where(and(
          eq(userApiKeys.userId, userId),
          eq(userApiKeys.guildId, guildId),
          eq(userApiKeys.provider, provider),
          eq(userApiKeys.isDefault, true)
        ));
    } catch (error) {
      this.logger.error('Failed to clear default keys:', error);
    }
  }

  /**
   * Set a key as default
   */
  async setDefaultKey(userId: string, guildId: string, keyId: string): Promise<boolean> {
    try {
      // Get the key to find its provider
      const key = await this.db.select().from(userApiKeys)
        .where(eq(userApiKeys.id, keyId))
        .limit(1);

      if (!key[0]) return false;

      // Clear existing defaults for this provider
      await this.clearDefaultKeys(userId, guildId, key[0].provider as AIProvider);

      // Set this key as default
      await this.db.update(userApiKeys)
        .set({ 
          isDefault: true, 
          updatedAt: new Date() 
        } as any)
        .where(eq(userApiKeys.id, keyId));

      this.logger.log(`🔑 Set API key ${keyId} as default for user ${userId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to set default key:', error);
      return false;
    }
  }

  /**
   * Update API key configuration
   */
  async updateApiKeyConfig(userId: string, guildId: string, keyId: string, config: APIKeyConfig): Promise<boolean> {
    try {
      await this.db.update(userApiKeys)
        .set({ 
          config: config,
          updatedAt: new Date() 
        } as any)
        .where(and(
          eq(userApiKeys.id, keyId),
          eq(userApiKeys.userId, userId),
          eq(userApiKeys.guildId, guildId)
        ));

      this.logger.log(`🔧 Updated API key configuration for ${keyId}`);
      return true;
    } catch (error) {
      this.logger.error('Failed to update API key configuration:', error);
      return false;
    }
  }
}
