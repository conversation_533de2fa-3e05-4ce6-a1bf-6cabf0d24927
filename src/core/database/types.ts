// Database JSONB field type definitions
// This file provides TypeScript interfaces for JSONB columns to ensure type safety

// Guild Settings Types
export type GuildSettings = {
  roleAccess?: {
    enabled: boolean,
      tiers: Array<{,
      id: string,
    name: string;
      roleIds: string[],
      permissions: string[],priority: number}>;
    defaultTier?: string;
    autoAssign?: boolean;
    logChannel?: string;
    restrictedChannels?: string[]};
  moderation?: {
    enabled: boolean,
      autoMod: boolean;
    logChannelId?: string;
    logChannel?: string;
    mutedRoleId?: string,warningThreshold: number};
  economy?: {
    enabled: boolean,
      currency: string,dailyReward: number,
    workCooldown: number};
  leveling?: {
    enabled: boolean,
      xpPerMessage: number;
    levelUpChannelId?: string,roleRewards: Array<{,
    level: number;
      roleId: string}>};
  devOnDemand?: {
    enabled?: boolean;
    requestChannel?: string;
    notificationChannel?: string;
    developerRole?: string;
    clientRole?: string;
    maxActiveRequests?: number;
    requestTimeoutHours?: number;
    autoAssignment?: boolean;
    autoAssignDevelopers?: boolean;
    requireApproval?: boolean;
    allowedChannels?: string[]};
  aiAutomation?: {
    enabled: boolean;
    weekly_insights?: boolean;
    engagement_digest?: boolean;
    member_insights?: boolean;
    auto_moderation?: boolean;
    auto_engagement?: boolean;
    content_suggestions?: boolean;
    smart_greetings?: boolean;
    smart_notifications?: boolean;
    auto_responses?: boolean;
    sentiment_analysis?: boolean;
    topic_tracking?: boolean;
    engagement_optimization?: boolean};
  reactionRole?: {
    enabled: boolean,
      roles: Array<{,
      messageId: string,
    channelId: string;
      emoji: string,
    roleId: string}>};
  whop?: {
    enabled: boolean;
    apiKey?: string;
    webhookUrl?: string;
    productIds?: string[]};
  meme?: {
    enabled: boolean;
    allowedChannels?: string[];
    cooldownSeconds?: number};
  userCommand?: {
    enabled: boolean;
    prefix?: string;
    allowedChannels?: string[];
    commands?: Array<{
      name: string,
      response: string;
      description?: string;
      permissions?: string[];
      cooldown?: number,enabled: boolean}>};
  gaming?: {
    enabled: boolean;
    allowedGames?: string[];
    tournamentChannelId?: string;
    leaderboardChannelId?: string};
  utility?: {
    enabled: boolean;
    allowedChannels?: string[];
    features?: string[]};
  music?: {
    enabled: boolean;
    maxQueueSize?: number;
    allowPlaylists?: boolean;
    djRoleId?: string;
    defaultVolume?: number};
  automationConfig?: {
    enabled: boolean,
      onGuildJoin: boolean,periodicCheck: boolean,
    checkInterval: number;
    features: Record<string, { enabled: boolean priority: number }>;
    delayBetweenSetups: number,
      adminNotifications: boolean,fallbackOnError: boolean};
  setupHistory?: Array<{
    trigger: string,
      timestamp: string,duration: number,
    successRate: number;
    featuresSetup: string[],
    featuresFailed: string[]}>;
  lastSetup?: {
    timestamp: string,
      trigger: string,successRate: number}}

// Guild Features Types
export type GuildFeatures = {
  // Role access configuration (camelCase and hyphen-case for compatibility)
  roleAccess?: GuildSettings['roleAccess'];
  'role-access'?: GuildSettings['roleAccess'];
  music?: {
    enabled: boolean;
  maxQueueSize: number;
  allowPlaylists: boolean;
    djRoleId?: string};
  gaming?: {
    enabled: boolean,
    allowedGames: string[];
    tournamentChannelId?: string};
  ai?: {
    enabled: boolean,
      defaultModel: string,maxTokens: number,
    allowedChannels: string[]};
  'reaction-role'?: {
    enabled: boolean,
      roles: Array<{,
      messageId: string,
    channelId: string;
      emoji: string,
    roleId: string}>};
  // User command configuration (hyphen-case and camelCase for compatibility)
  userCommand?: {
    enabled: boolean;
    prefix?: string;
    allowedChannels?: string[];
    commands?: Array<{
      name: string;
      response: string;
      description?: string;
      permissions?: string[];
      cooldown?: number;
      enabled: boolean;
    }>;
  };

  leveling?: {
    enabled: boolean;
    xpPerMessage: number;
    levelUpChannelId?: string;
    roleRewards: Array<{
      level: number;
      roleId: string;
    }>;
  };
  moderation?: {
    enabled: boolean;
    autoMod: boolean;
    logChannelId?: string;
    mutedRoleId?: string;
    warningThreshold: number;
  };
  economy?: {
    enabled: boolean;
    currency: string;
    dailyReward: number;
    workCooldown: number;
  };
  starboard?: {
    enabled: boolean;
    channelId?: string;
    threshold: number;
    emoji: string;
  };
  utility?: {
    enabled: boolean;
    allowedChannels?: string[];
    features?: string[];
  };
}

// AI Agent Configuration Types
export type AgentConfiguration = {
  channels?: string[];
  defaultAgent?: string;
  schedules?: Array<{
    type: 'daily' | 'weekly' | 'monthly';
    time: string;
    timezone?: string;
    enabled: boolean;
  }>;
  prompts?: Record<string, string>;
  personality?: {
    tone: string;
    style: string;
    expertise: string[];
    responseLength: 'short' | 'medium' | 'long';
  };
  triggers?: {
    keywords?: string[];
    reactions?: string[];
    conditions?: Record<string, any>;
    autoRespond?: boolean;
  };
  limits?: {
    maxResponsesPerHour: number;
    maxTokensPerResponse: number;
    cooldownSeconds: number;
  };
}

// Agent Permissions Types
export type AgentPermissions = {
  allowedRoles?: string[];
  restrictedRoles?: string[];
  allowedUsers?: string[];
  restrictedUsers?: string[];
  allowedChannels?: string[];
  restrictedChannels?: string[];
  requirePermission?: boolean}

// Agent Settings Types
export type AgentSettings = {
  enabled: boolean,
      debug: boolean,logLevel: 'error' | 'warn' | 'info' | 'debug',
    autoLearn: boolean;
  contextWindow: number,
      temperature: number,maxRetries: number}

// Agent Memory Value Types
export type MemoryValue = {
  // For assessment memories
  response?: string;
  score?: number;
  category?: string;
  
  // For progress tracking
  completed?: number;
  active?: number;
  streak?: number;
  weeklyPercent?: number;
  overall?: string;
  
  // For conversation memories
  context?: string;
  summary?: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  
  // For goal memories
  goals?: Array<{
    id: string,
      title: string;
    description: string;
  status: 'active' | 'completed' | 'paused',progress: number;
    dueDate?: string}>;
  
  // For interaction memories
  interactionType?: string;
  outcome?: string;
  feedback?: string;
  
  // Generic properties
  data?: Record<string, any>;
  metadata?: Record<string, any>}

// User Preferences Types
export type UserPreferences = {
  notifications?: {
    enabled: boolean,
      types: string[],frequency: 'immediate' | 'daily' | 'weekly'};
  privacy?: {
    shareProgress: boolean,
      allowDataCollection: boolean,showOnLeaderboard: boolean};
  ai?: {
    preferredAgent: string,
      responseStyle: 'formal' | 'casual' | 'friendly',maxResponseLength: number};
  timezone?: string;
  language?: string;
  // Economy-related preferences
  balance?: number;
  lastDaily?: string;
  dailyStreak?: number;
  warnings?: Array<{
    id: string,
      guildId: string,reason: string,
    moderatorId: string;
    timestamp: string,
    severity: 'low' | 'medium' | 'high'}>;
  devRequests?: Array<{
    id: string,
      clientId: string,clientTag: string,
    description: string;
    budget?: string;
    timeline?: string;
    skills: string[];
  status: 'open' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'payment_pending' | 'payment_held';
  createdAt: string;
    developerId?: string;
    developerTag?: string;
    assignedAt?: string;
    channelId?: string;
    paymentAmount?: number;
    escrowId?: string;
    milestones?: Array<{
      id: string,
      description: string,amount: number,
    status: 'pending' | 'completed' | 'released';
      completedAt?: string;
      releasedAt?: string}>;
    paymentStatus?: 'pending' | 'held' | 'released' | 'refunded';
    completedAt?: string;
    cancelledAt?: string}>;
  devProfile?: {
    isClient?: boolean;
    isDeveloper: boolean,
      skills: string[];
    portfolio?: string,rating: number,
    completedProjects: number}}

// User Profile Types
export type UserProfile = {
  displayName?: string;
  bio?: string;
  avatar?: string;
  badges?: string[];
  achievements?: Array<{
    id: string,
      name: string,description: string,
    unlockedAt: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary'}>;
  stats?: {
    messagesCount: number,
      commandsUsed: number,timeSpent: number,
    level: number;
    experience: number};
  goals?: Array<{
    id: string,
      title: string,category: string,
    status: 'active' | 'completed' | 'paused';
    progress: number,
    createdAt: string;
    targetDate?: string}>}

// Welcome Roles Types
export type WelcomeRoles = {
  autoAssign?: string[];
  selectable?: Array<{
    roleId: string;
  name: string;
  description: string;
    emoji?: string;
    category?: string}>}

// Session Metadata Types
export type SessionMetadata = {
  userAgent?: string;
  ipAddress?: string;
  deviceFingerprint?: string;
  loginMethod?: 'discord' | 'oauth' | 'token';
  lastActivity?: string;
  permissions?: string[]}
