import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { RedisService } from '../redis.service';
import { Session } from '../entities/session.entity';

@Injectable()
export class SessionRepository extends BaseRepository<Session> {
  constructor(redisService: RedisService) {
    super(redisService, 'sessions');
  }

  /**
   * Find session by session ID
   */
  async findBySessionId(sessionId: string): Promise<Session | null> {
    return this.findOne({
      where: { sessionId } as Partial<Session>
    });
  }

  /**
   * Find sessions by user ID
   */
  async findByUserId(userId: string): Promise<Session[]> {
    return this.find({
      where: { userId } as Partial<Session>
    });
  }

  /**
   * Find active sessions (not revoked and not expired)
   */
  async findActiveSessions(): Promise<Session[]> {
    const now = new Date();
    const sessions = await this.find();
    
    return sessions.filter((session: any) => 
      !session.isRevoked && 
      new Date(session.expiresAt) > now
    );
  }

  /**
   * Find active sessions for a specific user
   */
  async findActiveSessionsByUserId(userId: string): Promise<Session[]> {
    const now = new Date();
    const sessions = await this.findByUserId(userId);
    
    return sessions.filter((session: any) => 
      !session.isRevoked && 
      new Date(session.expiresAt) > now
    );
  }

  /**
   * Find expired sessions
   */
  async findExpiredSessions(): Promise<Session[]> {
    const now = new Date();
    const sessions = await this.find();
    
    return sessions.filter((session: any) => 
      new Date(session.expiresAt) <= now
    );
  }

  /**
   * Revoke session
   */
  async revokeSession(sessionId: string): Promise<Session | null> {
    const session = await this.findBySessionId(sessionId);
    if (!session) return null;

    return this.update(session.id, {
      isRevoked: true
    });
  }

  /**
   * Revoke all sessions for a user
   */
  async revokeAllUserSessions(userId: string): Promise<Session[]> {
    const sessions = await this.findActiveSessionsByUserId(userId);
    const revokedSessions: Session[] = [];
    
    for (const session of sessions) {
      const revoked = await this.revokeSession(session.sessionId);
      if (revoked) {
        revokedSessions.push(revoked);
      }
    }
    
    return revokedSessions;
  }

  /**
   * Update session access time
   */
  async updateAccessTime(sessionId: string): Promise<Session | null> {
    const session = await this.findBySessionId(sessionId);
    if (!session) return null;

    return this.update(session.id, {
      lastAccessedAt: new Date()
    });
  }

  /**
   * Clean up expired sessions (hard delete)
   */
  async cleanupExpiredSessions(): Promise<number> {
    const expiredSessions = await this.findExpiredSessions();
    let count = 0;
    
    for (const session of expiredSessions) {
      if (await this.delete(session.id, false)) {
        count++;
      }
    }
    
    return count;
  }

  /**
   * Clean up revoked sessions older than specified days
   */
  async cleanupRevokedSessions(days = 30): Promise<number> {
    const cutoff = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
    const sessions = await this.find();
    let count = 0;
    
    const revokedOldSessions = sessions.filter((session: any) => 
      session.isRevoked && 
      new Date(session.updatedAt) < cutoff
    );
    
    for (const session of revokedOldSessions) {
      if (await this.delete(session.id, false)) {
        count++;
      }
    }
    
    return count;
  }

  /**
   * Extend session expiry
   */
  async extendSession(sessionId: string, additionalMinutes: number): Promise<Session | null> {
    const session = await this.findBySessionId(sessionId);
    if (!session) return null;

    const newExpiryTime = new Date(new Date(session.expiresAt).getTime() + additionalMinutes * 60 * 1000);
    
    return this.update(session.id, {
      expiresAt: newExpiryTime.toISOString()
    });
  }

  /**
   * Find sessions by IP address
   */
  async findByIpAddress(ipAddress: string): Promise<Session[]> {
    return this.find({
      where: { ipAddress } as Partial<Session>
    });
  }

  /**
   * Find sessions by device fingerprint
   */
  async findByDeviceFingerprint(deviceFingerprint: string): Promise<Session[]> {
    return this.find({
      where: { deviceFingerprint } as Partial<Session>
    });
  }

  /**
   * Get session statistics
   */
  async getSessionStats(): Promise<{
    total: number;
    active: number;
    expired: number;
    revoked: number;
    uniqueUsers: number;
    uniqueIpAddresses: number;
    averageSessionDuration: number; // in minutes
  }> {
    const sessions = await this.find();
    const now = new Date();
    
    const active = sessions.filter((s: any) => !s.isRevoked && new Date(s.expiresAt) > now);
    const expired = sessions.filter((s: any) => new Date(s.expiresAt) <= now);
    const revoked = sessions.filter((s: any) => s.isRevoked);
    
    const uniqueUsers = new Set(sessions.map((s: any) => s.userId)).size;
    const uniqueIps = new Set(sessions.filter((s: any) => s.ipAddress).map((s: any) => s.ipAddress)).size;
    
    // Calculate average session duration for completed sessions
    const completedSessions = sessions.filter((s: any) => s.lastAccessedAt);
    let totalDuration = 0;
    
    for (const session of completedSessions) {
      const startTime = new Date(session.createdAt).getTime();
      const endTime = new Date(session.lastAccessedAt!).getTime();
      totalDuration += endTime - startTime;
    }
    
    const averageSessionDuration = completedSessions.length > 0 
      ? totalDuration / completedSessions.length / (1000 * 60) // Convert to minutes
      : 0;

    return {
      total: sessions.length,
      active: active.length,
      expired: expired.length,
      revoked: revoked.length,
      uniqueUsers,
      uniqueIpAddresses: uniqueIps,
      averageSessionDuration
    };
  }

  /**
   * Create session with automatic expiry
   */
  async createSession(data: {
    sessionId: string;
    userId: string;
    encryptedData?: string;
    ipAddress?: string;
    userAgent?: string;
    deviceFingerprint?: string;
    metadata?: any;
    expiryHours?: number;
  }): Promise<Session> {
    const expiryTime = new Date(Date.now() + (data.expiryHours || 24) * 60 * 60 * 1000);
    
    return this.create({
      sessionId: data.sessionId,
      userId: data.userId,
      encryptedData: data.encryptedData || null,
      expiresAt: expiryTime.toISOString(),
      ipAddress: data.ipAddress || null,
      userAgent: data.userAgent || null,
      deviceFingerprint: data.deviceFingerprint || null,
      isRevoked: false,
      lastAccessedAt: new Date(),
      metadata: data.metadata || null
    } as any);
  }
}
