import { Injectable } from '@nestjs/common';
import { BaseRepository } from './base.repository';
import { RedisService } from '../redis.service';
import { Guild } from '../entities/guild.entity';

@Injectable()
export class GuildRepository extends BaseRepository<Guild> {
  constructor(redisService: RedisService) {
    super(redisService, 'guilds');
  }

  /**
   * Find guild by Discord ID
   */
  async findByDiscordId(discordId: string): Promise<Guild | null> {
    return this.findOne({
      where: { discordId } as Partial<Guild>
    });
  }

  /**
   * Find guild by name
   */
  async findByName(name: string): Promise<Guild | null> {
    return this.findOne({
      where: { name } as Partial<Guild>
    });
  }

  /**
   * Find active guilds
   */
  async findActiveGuilds(): Promise<Guild[]> {
    return this.find({
      where: { isActive: true } as Partial<Guild>
    });
  }

  /**
   * Find guilds by owner Discord ID
   */
  async findByOwner(ownerDiscordId: string): Promise<Guild[]> {
    return this.find({
      where: { ownerDiscordId } as Partial<Guild>
    });
  }

  /**
   * Find guilds with welcome enabled
   */
  async findWithWelcomeEnabled(): Promise<Guild[]> {
    return this.find({
      where: { welcomeEnabled: true } as Partial<Guild>
    });
  }

  /**
   * Find guilds with starboard enabled
   */
  async findWithStarboardEnabled(): Promise<Guild[]> {
    return this.find({
      where: { starboardEnabled: true } as Partial<Guild>
    });
  }

  /**
   * Update guild activity
   */
  async updateActivity(id: string | number): Promise<Guild | null> {
    return this.update(id, {
      lastActivityAt: new Date()
    });
  }

  /**
   * Update guild settings
   */
  async updateSettings(id: string | number, settings: any): Promise<Guild | null> {
    const guild = await this.findById(id);
    if (!guild) return null;

    const updatedSettings = {
      ...guild.settings,
      ...settings
    };

    return this.update(id, { settings: updatedSettings });
  }

  /**
   * Update guild features
   */
  async updateFeatures(id: string | number, features: any): Promise<Guild | null> {
    const guild = await this.findById(id);
    if (!guild) return null;

    const updatedFeatures = {
      ...guild.features,
      ...features
    };

    return this.update(id, { features: updatedFeatures });
  }

  /**
   * Enable/disable welcome feature
   */
  async toggleWelcome(
    id: string | number, 
    enabled: boolean, 
    channelId?: string, 
    message?: string,
    roles?: any
  ): Promise<Guild | null> {
    const updates: any = { welcomeEnabled: enabled };
    
    if (channelId) updates.welcomeChannelId = channelId;
    if (message) updates.welcomeMessage = message;
    if (roles) updates.welcomeRoles = roles;

    return this.update(id, updates);
  }

  /**
   * Enable/disable starboard feature
   */
  async toggleStarboard(
    id: string | number, 
    enabled: boolean, 
    channelId?: string, 
    threshold?: number
  ): Promise<Guild | null> {
    const updates: any = { starboardEnabled: enabled };
    
    if (channelId) updates.starboardChannelId = channelId;
    if (threshold) updates.starboardThreshold = threshold;

    return this.update(id, updates);
  }

  /**
   * Get guild statistics
   */
  async getGuildStats(): Promise<{
    total: number;
    active: number;
    inactive: number;
    withWelcome: number;
    withStarboard: number;
    recentlyActive: number;
  }> {
    const guilds = await this.find();
    const activeGuilds = guilds.filter((g: any) => g.isActive);
    const withWelcome = guilds.filter((g: any) => g.welcomeEnabled);
    const withStarboard = guilds.filter((g: any) => g.starboardEnabled);
    
    // Recently active (within last 24 hours)
    const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000);
    const recentlyActive = guilds.filter((g: any) => 
      g.lastActivityAt && new Date(g.lastActivityAt) > yesterday
    );

    return {
      total: guilds.length,
      active: activeGuilds.length,
      inactive: guilds.length - activeGuilds.length,
      withWelcome: withWelcome.length,
      withStarboard: withStarboard.length,
      recentlyActive: recentlyActive.length
    };
  }

  /**
   * Search guilds by name
   */
  async searchGuilds(query: string, limit = 20): Promise<Guild[]> {
    const guilds = await this.find();
    const searchLower = query.toLowerCase();
    
    return guilds
      .filter((guild: any) => guild.name.toLowerCase().includes(searchLower))
      .slice(0, limit);
  }
}
