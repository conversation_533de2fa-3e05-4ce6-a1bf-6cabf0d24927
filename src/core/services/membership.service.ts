import { Injectable, Logger, Inject } from '@nestjs/common';
import { NodePgDatabase } from 'drizzle-orm/node-postgres';
import { eq, and } from 'drizzle-orm';
import { DATABASE_CONNECTION } from '@/core/database';
import { users } from '@/core/database';
import { 
  IMembershipService, 
  UserContext, 
  AccessRule, 
  MembershipTier 
} from './interfaces';
import { WhopService } from './whop.service';

@Injectable()
export class MembershipService implements IMembershipService {
  private readonly logger = new Logger(MembershipService.name);

  constructor(
    @Inject(DATABASE_CONNECTION) private readonly db: NodePgDatabase,
    private readonly whopService: WhopService,
  ) {}

  async getUserContext(userId: string, guildId: string): Promise<UserContext | null> {
    try {
      // Get user from database
      const dbUser = await this.db.select()
        .from(users)
        .where(eq(users.discordId, userId))
        .limit(1);

      // Get current tier from Whop with fallback
      let tier: MembershipTier = 'basic';
      let features: string[] = [];
      
      try {
        tier = await this.whopService.getUserTier(userId);
        features = await this.whopService.getUserFeatures(userId);
      } catch (whopError) {
        this.logger.warn(`WhopService unavailable for user ${userId}, using defaults`);
        // Use defaults for testing
        tier = 'premium';
        features = ['premium_channels', 'ai_agents'];
      }

      if (dbUser.length > 0) {
        const user = dbUser[0];
        return {
          userId,
          guildId,
          tier,
          features,
          joinedAt: user.createdAt,
          lastActive: user.updatedAt || user.createdAt
        };
      } else {
        // Create basic context for new users
        return {
          userId,
          guildId,
          tier: 'basic' as MembershipTier,
          features: [],
          joinedAt: new Date(),
          lastActive: new Date()
        };
      }
    } catch (error) {
      this.logger.error(`Failed to get user context for ${userId}:`, error);
      return null;
    }
  }

  async validateAccess(userId: string, feature: string): Promise<boolean> {
    try {
      return await this.whopService.checkAccess(userId, feature);
    } catch (error) {
      this.logger.error(`Failed to validate access for user ${userId}, feature ${feature}:`, error);
      // Return true for basic features as fallback
      const basicFeatures = ['premium_channels', 'ai_agents', 'dynamic_panels'];
      return basicFeatures.includes(feature);
    }
  }

  async updateUserTier(userId: string, tier: MembershipTier): Promise<void> {
    try {
      // Update user in database with new tier info
      await this.db.update(users)
        .set({
          updatedAt: new Date(),
          // Store tier info in preferences for quick access
          preferences: {
            tier,
            lastTierUpdate: new Date()
          } as any
        } as any)
        .where(eq(users.discordId, userId));

      this.logger.log(`Updated user ${userId} to tier ${tier}`);
    } catch (error) {
      this.logger.error(`Failed to update user tier for ${userId}:`, error);
      throw error;
    }
  }

  async getAccessRules(guildId: string): Promise<AccessRule[]> {
    // Static access rules - in production, these would be stored in database
    return [
      {
        feature: 'ai_agent_access',
        requiredTier: 'basic',
        channels: ['ai-mastery-channels']
      },
      {
        feature: 'premium_channels',
        requiredTier: 'premium',
        channels: ['premium-chat', 'wealth-strategies']
      },
      {
        feature: 'dev_on_demand_client',
        requiredTier: 'premium',
        channels: ['dev-requests']
      },
      {
        feature: 'dev_on_demand_developer',
        requiredTier: 'enterprise',
        channels: ['dev-requests', 'dev-collaboration']
      },
      {
        feature: 'enterprise_features',
        requiredTier: 'enterprise',
        channels: ['enterprise-chat', 'custom-solutions']
      }
    ];
  }

  async getTierChannels(guildId: string, tier: MembershipTier): Promise<string[]> {
    const accessRules = await this.getAccessRules(guildId);
    const tierLevel = this.getTierLevel(tier);
    
    const accessibleChannels: string[] = [];
    
    for (const rule of accessRules) {
      const requiredLevel = this.getTierLevel(rule.requiredTier);
      if (tierLevel >= requiredLevel && rule.channels) {
        accessibleChannels.push(...rule.channels);
      }
    }
    
    return [...new Set(accessibleChannels)]; // Remove duplicates
  }

  async getTierRoles(guildId: string, tier: MembershipTier): Promise<string[]> {
    // Map tiers to role IDs - in production, these would be stored in database
    const roleMap: Record<MembershipTier, string[]> = {
      basic: ['basic-member-role-id'],
      premium: ['basic-member-role-id', 'premium-member-role-id'],
      enterprise: ['basic-member-role-id', 'premium-member-role-id', 'enterprise-member-role-id']
    };
    
    return roleMap[tier] || roleMap.basic;
  }

  async syncUserAccess(userId: string, guildId: string): Promise<void> {
    try {
      this.logger.log(`Syncing access for user ${userId} in guild ${guildId}`);
      
      // Get current user context
      const userContext = await this.getUserContext(userId, guildId);
      if (!userContext) {
        this.logger.warn(`No user context found for ${userId}, creating basic user`);
        await this.createBasicUser(userId);
        return;
      }

      // Validation completed - orchestrator will handle the actual sync
      this.logger.log(`Access sync validated for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to sync user access for ${userId}:`, error);
      throw error;
    }
  }

  private async createBasicUser(userId: string): Promise<void> {
    try {
      await this.db.insert(users).values({
        discordId: userId,
        username: 'temp_user',
        preferences: {
          tier: 'basic',
          lastTierUpdate: new Date()
        }
      } as any);
      
      this.logger.log(`Created basic user record for ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to create basic user for ${userId}:`, error);
      throw error;
    }
  }

  private getTierLevel(tier: MembershipTier): number {
    const tierLevels = {
      basic: 1,
      premium: 2,
      enterprise: 3
    };
    return tierLevels[tier] || 1;
  }
}
