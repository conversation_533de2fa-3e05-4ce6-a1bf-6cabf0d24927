import {
    Body,
    Controller,
    Delete,
    Get,
    Param,
    Post,
    Put,
    Req,
    UseGuards
} from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';
import { GuildsService } from './guilds.service';

@ApiTags('guilds')
@Controller('guilds')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class GuildsController {
  constructor(private readonly guildsService: GuildsService) {}

  @Get(':guildId')
  @ApiOperation({ summary: 'Get guild information' })
  @ApiResponse({ status: 200, description: 'Guild information retrieved' })
  async getGuild(@Param('guildId') guildId: string, @Req() req: Request) {
    console.log('[GuildsController] getGuild called, user:', req.user);
    console.log('[GuildsController] Authorization header:', req.get('Authorization'));
    return await this.guildsService.getGuildInfo(guildId, req.user);
  }
  @Get(':guildId/features')
  @ApiOperation({ summary: 'Get enabled features for guild' })
  @ApiResponse({ status: 200, description: 'Guild features retrieved' })
  async getGuildFeatures(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getEnabledFeatures(guildId, req.user);
  }
  @Get(':guildId/features/:feature')
  @ApiOperation({ summary: 'Get specific feature configuration for guild' })
  @ApiResponse({ status: 200, description: 'Feature configuration retrieved (returns default config if not configured)' })
  async getGuildFeature(
    @Param('guildId') guildId: string,
    @Param('feature') feature: string,
    @Req() req: Request,
  ) {
    return await this.guildsService.getFeatureConfig(guildId, feature, req.user);
  }
  @Post(':guildId/features/:feature')
  @ApiOperation({ summary: 'Enable a guild feature' })
  @ApiResponse({ status: 200, description: 'Feature enabled successfully' })
  @ApiResponse({ status: 404, description: 'Guild or feature not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async enableFeaturePost(
    @Param('guildId') guildId: string,
    @Param('feature') feature: string,
    @Req() req: Request,
  ) {
    return await this.guildsService.enableFeature(guildId, feature, req.user);
  }

  @Delete(':guildId/features/:feature')
  @ApiOperation({ summary: 'Disable a guild feature' })
  @ApiResponse({ status: 200, description: 'Feature disabled successfully' })
  @ApiResponse({ status: 404, description: 'Guild or feature not found' })
  @ApiResponse({ status: 403, description: 'Insufficient permissions' })
  async disableFeatureDelete(
    @Param('guildId') guildId: string,
    @Param('feature') feature: string,
    @Req() req: Request,
  ) {
    return await this.guildsService.disableFeature(guildId, feature, req.user);
  }
  @Put(':guildId/features/:feature')
  @ApiOperation({ summary: 'Update guild feature configuration' })
  @ApiResponse({ status: 200, description: 'Feature updated successfully' })
  async updateFeature(
    @Param('guildId') guildId: string,
    @Param('feature') feature: string,
    @Body() config: any,
    @Req() req: Request,
  ) {
    return await this.guildsService.updateFeature(guildId, feature, config, req.user);
  }

  @Put(':guildId/features/:feature/enable')
  @ApiOperation({ summary: 'Enable a guild feature' })
  @ApiResponse({ status: 200, description: 'Feature enabled successfully' })
  async enableFeature(
    @Param('guildId') guildId: string,
    @Param('feature') feature: string,
    @Req() req: Request,
  ) {
    return await this.guildsService.enableFeature(guildId, feature, req.user);
  }
  @Put(':guildId/features/:feature/disable')
  @ApiOperation({ summary: 'Disable a guild feature' })
  @ApiResponse({ status: 200, description: 'Feature disabled successfully' })
  async disableFeature(
    @Param('guildId') guildId: string,
    @Param('feature') feature: string,
    @Req() req: Request,
  ) {
    return await this.guildsService.disableFeature(guildId, feature, req.user);
  }

  @Get(':guildId/channels')
  @ApiOperation({ summary: 'Get guild channels' })
  @ApiResponse({ status: 200, description: 'Guild channels retrieved' })
  async getChannels(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getChannels(guildId, req.user);
  }

  @Get(':guildId/roles')
  @ApiOperation({ summary: 'Get guild roles' })
  @ApiResponse({ status: 200, description: 'Guild roles retrieved' })
  async getRoles(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getRoles(guildId, req.user);
  }
  @Get(':guildId/members')
  @ApiOperation({ summary: 'Get guild members' })
  @ApiResponse({ status: 200, description: 'Guild members retrieved' })
  async getMembers(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getMembers(guildId, req.user);
  }

  @Get(':guildId/stats')
  @ApiOperation({ summary: 'Get guild statistics' })
  @ApiResponse({ status: 200, description: 'Guild statistics retrieved' })
  async getStats(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getStats(guildId, req.user);
  }

  @Get(':guildId/server-info')
  @ApiOperation({ summary: 'Get server information' })
  @ApiResponse({ status: 200, description: 'Server information retrieved' })
  async getServerInfo(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getGuildInfo(guildId, req.user);
  }

  @Get(':guildId/features/ai-agents/models')
  @ApiOperation({ summary: 'Get available AI models for AI agents feature' })
  @ApiResponse({ status: 200, description: 'Available AI models retrieved with capabilities and pricing' })
  async getAvailableAIModels(@Param('guildId') guildId: string, @Req() req: Request) {
    return await this.guildsService.getAvailableAIModels(guildId, req.user);
  }
}