import { CreateGuild, GuildFeatures } from '@/core/database/schema';
import { ForbiddenException, Injectable, Logger } from '@nestjs/common';
// import { eq, and, or, desc, count } from 'drizzle-orm';
import { CacheService } from '@/core/cache/cache.service';
import { DatabaseService } from '@/core/data';
import { DiscordService } from '../../discord/discord.service';
import { DiscordUtilsService } from '../../discord/utils/discord-utils.service';

@Injectable()
export class GuildsService {
  private readonly logger = new Logger(GuildsService.name);

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly redisDatabaseService: CacheService,
    private readonly discordService: DiscordService,
    private readonly discordUtils: DiscordUtilsService
  ) {}

  private getDefaultFeatureConfig(feature: string): any {
    const defaultConfigs: Record<string, any> = {
      'music': {
        enabled: false,
        volume: 50,
        maxQueueSize: 100,
        allowedChannels: [],
        allowedRoles: [],
        djRole: null,
        autoLeave: true,
        autoLeaveTime: 300
      },
      'welcome-message': {
        enabled: false,
        channel: null,
        message: 'Welcome to the server, {user}!',
        embedEnabled: false,
        embedColor: '#00ff00',
        dmEnabled: false,
        dmMessage: null
      },
      'gaming': {
        enabled: false,
        allowedChannels: [],
        availableGames: ['8ball', 'trivia', 'wordguess', 'slots'],
        economyIntegration: false
      },
      'reaction-role': {
        enabled: false,
        roles: []
      },
      'meme': {
        enabled: false,
        allowedChannels: [],
        autoPost: false,
        autoPostInterval: 3600,
        sources: ['reddit'],
        nsfw: false
      },
      'user-command': {
        enabled: false,
        prefix: '!',
        allowedChannels: [],
        showMemberCount: true,
        showRoleCount: true,
        showOnlineCount: true,
        showChannelCount: true,
        showBoostInfo: true,
        showServerAge: true,
        showBotCount: true,
        useEmbedFormat: true,
        embedColor: '#00ff00',
        customFooter: null,
        showThumbnail: true,
        commandName: 'serverinfo',
        allowedRoles: '',
        restrictToChannels: '',
        autoPostEnabled: false,
        autoPostChannel: null,
        autoPostInterval: 86400
      },
      'leveling': {
        enabled: false,
        xpPerMessage: 10,
        levelUpChannelId: null,
        roleRewards: [],
        messageCooldown: 60,
        xpMultiplier: 1.0,
        announceLevelUps: true,
        stackRoles: false
      },
      'moderation': {
        enabled: false,
        autoMod: false,
        logChannelId: null,
        mutedRoleId: null,
        warningThreshold: 3,
        autoKickThreshold: 5,
        autoBanThreshold: 7,
        deleteWarningsAfter: 2592000,
        spamDetection: false,
        linkDetection: false,
        capsDetection: false
      },
      'economy': {
        enabled: false,
        currency: 'coins',
        dailyReward: 100,
        workCooldown: 3600,
        startingBalance: 1000,
        dailyCooldown: 86400,
        crimeCooldown: 7200,
        robCooldown: 14400,
        shopEnabled: true,
        gamblingEnabled: true
      },
      'utility': {
        enabled: false,
        allowedChannels: [],
        features: ['weather', 'translate', 'qr', 'color'],
        apiKeys: {}
      },
      'starboard': {
        enabled: false,
        channelId: null,
        threshold: 3,
        emoji: '⭐',
        selfStar: false,
        botStar: false,
        excludeChannels: [],
        requireImage: false
      },
      'whop': {
        enabled: false,
        companyId: null,
        accessPassId: null,
        roleMapping: {},
        autoSync: true,
        syncInterval: 3600
      },
      'role-based-access': {
        enabled: false,
        tiers: [],
        premiumRoles: [],
        restrictions: {},
        autoAssign: false
      },
      'ai-agents': {
        enabled: false,
        provider: 'anthropic',
        model: 'claude-3-5-haiku-20241022',
        temperature: 0.7,
        maxTokens: 1500,
        systemPrompt: 'You are a helpful Discord bot assistant.',
        allowedChannels: [],
        allowedRoles: [],
        rateLimitPerUser: 10,
        rateLimitWindow: 3600,
        moderationEnabled: true,
        // Available models with pricing and capabilities
        availableModels: {
          // OpenAI Models
          'gpt-4': {
            provider: 'openai',
            name: 'GPT-4',
            inputPrice: 0.03,
            outputPrice: 0.06,
            maxInputTokens: 8192,
            maxOutputTokens: 4096,
            contextWindow: 8192
          },
          'gpt-3.5-turbo': {
            provider: 'openai',
            name: 'GPT-3.5 Turbo',
            inputPrice: 0.0015,
            outputPrice: 0.002,
            maxInputTokens: 4096,
            maxOutputTokens: 4096,
            contextWindow: 4096
          },
          // Claude 3 Models
          'claude-3-haiku-20240307': {
            provider: 'anthropic',
            name: 'Claude 3 Haiku',
            inputPrice: 0.25,
            outputPrice: 1.25,
            maxInputTokens: 200000,
            maxOutputTokens: 4096,
            contextWindow: 200000
          },
          'claude-3-sonnet-20240229': {
            provider: 'anthropic',
            name: 'Claude 3 Sonnet',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 4096,
            contextWindow: 200000
          },
          'claude-3-opus-20240229': {
            provider: 'anthropic',
            name: 'Claude 3 Opus',
            inputPrice: 15.00,
            outputPrice: 75.00,
            maxInputTokens: 200000,
            maxOutputTokens: 4096,
            contextWindow: 200000
          },
          // Claude 3.5 Models
          'claude-3-5-haiku-20241022': {
            provider: 'anthropic',
            name: 'Claude 3.5 Haiku',
            inputPrice: 0.80,
            outputPrice: 4.00,
            maxInputTokens: 200000,
            maxOutputTokens: 8192,
            contextWindow: 200000
          },
          'claude-3-5-sonnet-20240620': {
            provider: 'anthropic',
            name: 'Claude 3.5 Sonnet',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 8192,
            contextWindow: 200000
          },
          'claude-3-5-sonnet-20241022': {
            provider: 'anthropic',
            name: 'Claude 3.5 Sonnet v2',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 8192,
            contextWindow: 200000
          },
          // Claude 3.7 Models
          'claude-3-7-sonnet-20250219': {
            provider: 'anthropic',
            name: 'Claude 3.7 Sonnet',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 64000,
            contextWindow: 200000
          },
          // Claude 4 Models
          'claude-opus-4-20250514': {
            provider: 'anthropic',
            name: 'Claude 4 Opus',
            inputPrice: 15.00,
            outputPrice: 75.00,
            maxInputTokens: 200000,
            maxOutputTokens: 32000,
            contextWindow: 200000
          },
          'claude-sonnet-4-20250514': {
            provider: 'anthropic',
            name: 'Claude 4 Sonnet',
            inputPrice: 3.00,
            outputPrice: 15.00,
            maxInputTokens: 200000,
            maxOutputTokens: 64000,
            contextWindow: 200000
          }
        }
      },
      'dev-on-demand': {
        enabled: false,
        projectCategories: ['web', 'mobile', 'backend', 'frontend', 'fullstack', 'other'],
        skillLevels: ['beginner', 'intermediate', 'advanced', 'expert'],
        maxActiveProjects: 5,
        autoMatchmaking: false,
        requireApproval: true,
        developerRole: null,
        clientRole: null
      },
      'content-organization': {
        enabled: false,
        autoCreateCategories: false,
        categoryNamingScheme: 'default',
        channelNamingRules: {},
        archiveInactive: false,
        archiveAfterDays: 30,
        maxChannelsPerCategory: 50
      }
    };

    return defaultConfigs[feature] || { enabled: false };
  }

  private getAvailableModels(): Record<string, any> {
    const aiAgentsConfig = this.getDefaultFeatureConfig('ai-agents');
    return aiAgentsConfig.availableModels || {};
  }

  private validateAIAgentsConfig(config: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];
    const availableModels = this.getAvailableModels();

    // Validate model selection
    if (config.model && !availableModels[config.model]) {
      const availableModelNames = Object.keys(availableModels).join(', ');
      errors.push(`Invalid model '${config.model}'. Available models: ${availableModelNames}`);
    }

    // Validate provider matches model
    if (config.model && config.provider && availableModels[config.model]) {
      const modelInfo = availableModels[config.model];
      if (modelInfo.provider !== config.provider) {
        errors.push(`Provider '${config.provider}' does not match model '${config.model}' which requires provider '${modelInfo.provider}'`);
      }
    }

    // Validate token limits
    if (config.model && config.maxTokens && availableModels[config.model]) {
      const modelInfo = availableModels[config.model];
      if (config.maxTokens > modelInfo.maxOutputTokens) {
        errors.push(`Max tokens ${config.maxTokens} exceeds model limit of ${modelInfo.maxOutputTokens} for ${config.model}`);
      }
    }

    // Validate temperature range
    if (config.temperature !== undefined) {
      if (config.temperature < 0 || config.temperature > 2) {
        errors.push('Temperature must be between 0 and 2');
      }
    }

    // Validate rate limiting
    if (config.rateLimitPerUser !== undefined && config.rateLimitPerUser < 1) {
      errors.push('Rate limit per user must be at least 1');
    }

    if (config.rateLimitWindow !== undefined && config.rateLimitWindow < 60) {
      errors.push('Rate limit window must be at least 60 seconds');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  async getAvailableAIModels(guildId: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);
      
      const availableModels = this.getAvailableModels();
      
      // Add cost estimation helpers
      const modelsWithEstimation = Object.entries(availableModels).map(([modelId, model]) => ({
        id: modelId,
        ...model,
        costEstimation: {
          inputCostPer1K: (model.inputPrice / 1000).toFixed(4),
          outputCostPer1K: (model.outputPrice / 1000).toFixed(4),
          estimatedCostFor1000Messages: this.estimateUsageCost(model, 1000)
        }
      }));

      return {
        models: modelsWithEstimation,
        defaultModel: 'claude-3-5-haiku-20241022',
        providers: {
          anthropic: {
            name: 'Anthropic',
            models: modelsWithEstimation.filter((m: any) => m.provider === 'anthropic')
          },
          openai: {
            name: 'OpenAI',
            models: modelsWithEstimation.filter((m: any) => m.provider === 'openai')
          }
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get available AI models for guild ${guildId}:`, error);
      throw error;
    }
  }

  private estimateUsageCost(model: any, messageCount: number): string {
    // Estimate average message: 100 input tokens, 50 output tokens
    const avgInputTokens = 100;
    const avgOutputTokens = 50;
    
    const inputCost = (messageCount * avgInputTokens * model.inputPrice) / 1000000;
    const outputCost = (messageCount * avgOutputTokens * model.outputPrice) / 1000000;
    
    return `$${(inputCost + outputCost).toFixed(4)}`;
  }

  async getGuildInfo(guildId: string, user: any): Promise<any> {
    try {
      // Check permissions
      await this.checkGuildPermissions(guildId, user);

      // Get guild from Discord
      const discordGuild = await this.discordService.getGuildInfo(guildId);
      
      // Get guild from Redis
      const dbGuild = await this.getGuildFromRedis(guildId);
      return {
        ...discordGuild,
        features: Array.isArray(discordGuild.features) ? discordGuild.features : [],
        settings: dbGuild?.settings || {},
        dbFeatures: dbGuild?.features || {},
        lastActivity: dbGuild?.lastActivityAt
      };
    } catch (error) {
      this.logger.error(`Failed to get guild info for ${guildId}:`, error);
      throw error;
    }
  }

  async getEnabledFeatures(guildId: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);
      
      const features = await this.discordService.getEnabledFeatures(guildId);
      return { guildId, features: Array.isArray(features) ? features : [] };
    } catch (error) {
      this.logger.error(`Failed to get features for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getFeatureConfig(guildId: string, feature: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);
      
      // Get guild from Redis
      const guild = await this.getGuildFromRedis(guildId);
      
      // Get existing config or default config
      const config = guild?.features?.[feature] || this.getDefaultFeatureConfig(feature);
      return {
        guildId,
        feature,
        config,
        enabled: config.enabled || false
      };
    } catch (error) {
      this.logger.error(`Failed to get feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  async updateFeature(guildId: string, feature: string, config: any, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);

      // Validate configuration for AI agents
      if (feature === 'ai-agents') {
        const validation = this.validateAIAgentsConfig(config);
        if (!validation.isValid) {
          const error = new Error(`Invalid AI agents configuration: ${validation.errors.join(', ')}`);
          this.logger.error(`Configuration validation failed for guild ${guildId}:`, validation.errors);
          throw error;
        }
      }

      // Update guild configuration in Redis
      let guild = await this.getGuildFromRedis(guildId);
      if (!guild) {
        // Create guild entry if it doesn't exist
        const newGuild = {
          id: `guild_${Date.now()}`,
          discordId: guildId,
          name: 'Unknown Guild',
          features: { [feature as keyof GuildFeatures]: config },
          isActive: true,
          welcomeEnabled: false,
          starboardEnabled: false,
          starboardThreshold: 3
        } as CreateGuild;
        await this.saveGuildToRedis(guildId, newGuild);
        guild = newGuild;
      } else {
        const updatedFeatures: GuildFeatures = {
          ...guild.features,
          [feature as keyof GuildFeatures]: config
        };
        await this.updateGuildInRedis(guildId, { features: updatedFeatures });
        guild = { ...guild, features: updatedFeatures };
      }

      return {
        guildId,
        feature,
        config,
        message: 'Feature updated successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to update feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getChannels(guildId: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);
      return await this.discordUtils.getGuildChannels(guildId);
    } catch (error) {
      this.logger.error(`Failed to get channels for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getRoles(guildId: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);
      return await this.discordUtils.getGuildRoles(guildId);
    } catch (error) {
      this.logger.error(`Failed to get roles for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getMembers(guildId: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);
      // For security, return limited member info
      return {
        guildId,
        memberCount: await this.getMemberCount(guildId),
        message: 'Full member list requires additional permissions'
      };
    } catch (error) {
      this.logger.error(`Failed to get members for guild ${guildId}:`, error);
      throw error;
    }
  }

  async getStats(guildId: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);

      const guild = await this.getGuildFromRedis(guildId);
      const discordGuild = await this.discordService.getGuildInfo(guildId);
      return {
        guildId,
        memberCount: discordGuild.memberCount || 0,
        features: Object.keys(guild?.features || {}),
        lastActivity: guild?.lastActivityAt,
        isActive: guild?.isActive || false
      };
    } catch (error) {
      this.logger.error(`Failed to get stats for guild ${guildId}:`, error);
      throw error;
    }
  }

  private async checkGuildPermissions(guildId: string, user: any): Promise<boolean> {
    // OAuth users are already validated by Discord
    if (user.sessionId === 'discord-session') {
      return true;
    }
    
    try {
      const hasPermissions = await this.discordUtils.checkBotGuildPermissions(user.userId || user.id, guildId);
      if (!hasPermissions) {
        throw new ForbiddenException('Insufficient permissions to access this guild');
      }
      return true;
    } catch (error) {
      this.logger.error(`Permission check failed for user ${user.userId || user.id} in guild ${guildId}:`, error);
      throw new ForbiddenException('Unable to verify guild permissions');
    }
  }

  async enableFeature(guildId: string, feature: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);

      let guild = await this.getGuildFromRedis(guildId);
      if (!guild) {
        const newGuild = {
          id: `guild_${Date.now()}`,
          discordId: guildId,
          name: 'Unknown Guild',
          features: { [feature as keyof GuildFeatures]: { enabled: true } },
          isActive: true,
          welcomeEnabled: false,
          starboardEnabled: false,
          starboardThreshold: 3
        } as CreateGuild;
        await this.saveGuildToRedis(guildId, newGuild);
        guild = newGuild;
      } else {
        const updatedFeatures: GuildFeatures = {
          ...guild.features,
          [feature as keyof GuildFeatures]: { ...(guild.features?.[feature as keyof GuildFeatures] || {}), enabled: true }
        };
        await this.updateGuildInRedis(guildId, { features: updatedFeatures });
      }

      return {
        guildId,
        feature,
        enabled: true,
        message: 'Feature enabled successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to enable feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  async disableFeature(guildId: string, feature: string, user: any): Promise<any> {
    try {
      await this.checkGuildPermissions(guildId, user);

      const guild = await this.getGuildFromRedis(guildId);

      if (guild) {
        const updatedFeatures: GuildFeatures = {
          ...guild.features,
          [feature as keyof GuildFeatures]: { ...(guild.features?.[feature as keyof GuildFeatures] || {}), enabled: false }
        };
        await this.updateGuildInRedis(guildId, { features: updatedFeatures });
      }

      return {
        guildId,
        feature,
        enabled: false,
        message: 'Feature disabled successfully'
      };
    } catch (error) {
      this.logger.error(`Failed to disable feature ${feature} for guild ${guildId}:`, error);
      throw error;
    }
  }

  private async getMemberCount(guildId: string): Promise<number> {
    try {
      const guildInfo = await this.discordService.getGuildInfo(guildId);
      return guildInfo.memberCount || 0;
    } catch (error) {
      this.logger.error(`Failed to get member count for guild ${guildId}:`, error);
      return 0;
    }
  }

  // Redis helper methods
  private async getGuildFromRedis(guildId: string): Promise<any | null> {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const guildKey = `guild:${guildId}`;
      const guildData = await redis.get(guildKey);
      return guildData ? JSON.parse(guildData) : null;
    } catch (error) {
      this.logger.error(`Failed to get guild from Redis: ${guildId}`, error);
      return null;
    }
  }

  private async saveGuildToRedis(guildId: string, guildData: any): Promise<void> {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const guildKey = `guild:${guildId}`;
      await redis.set(guildKey, JSON.stringify(guildData));
    } catch (error) {
      this.logger.error(`Failed to save guild to Redis: ${guildId}`, error);
      throw error;
    }
  }

  private async updateGuildInRedis(guildId: string, updates: any): Promise<void> {
    try {
      const redis = this.redisDatabaseService.redis.getClient();
      const guildKey = `guild:${guildId}`;
      const existingData = await redis.get(guildKey);
      const currentData = existingData ? JSON.parse(existingData) : {};
      const updatedData = { ...currentData, ...updates };
      await redis.set(guildKey, JSON.stringify(updatedData));
    } catch (error) {
      this.logger.error(`Failed to update guild in Redis: ${guildId}`, error);
      throw error;
    }
  }
}